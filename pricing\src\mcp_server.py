import json
from fastmcp import FastMCP
from providers import get_providers
import prices

# Create an MCP server
mcp = FastMCP("Pricing Service")

# Tool to calculate product pricing
@mcp.tool()
def calculate_price(subscription: str, country_code: str, term: str, seats: int, company_name: str) -> str:
    """
    Calculate the total price for a product subscription.

    Args:
        subscription: The product the user is interested in
        country_code: Two letter code of the country the user is in.
        term: term of the subscription (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats to be purchased
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")

    Returns:
        Pricing information including total price
    """
    try:
        result = prices.calculate_price(subscription, country_code, term, seats, company_name)
        return json.dumps(result, indent=2)
    except (KeyError, AttributeError) as e:
        return f"Error: {str(e)}"

# Tool to list available subscription options
@mcp.tool()
def list_term_options(company_name: str) -> str:
    """List all available terms for a company
    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
    Returns:
        str: List of available terms
    """
    try:
        options = prices.get_term_options(company_name)
        return json.dumps(options, indent=2)
    except KeyError as e:
        return f"Error: {str(e)}"

# Tool to list available countries
@mcp.tool()
def list_countries(company_name: str) -> str:
    """List all countries where pricing is available for a company
    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
    Returns:
        json with available country codes
    """
    try:
        country_codes = prices.get_countries(company_name)
        return json.dumps(country_codes, indent=2)
    except KeyError as e:
        return f"Error: {str(e)}"

# Resource for pricing by country
@mcp.tool()
def get_country_pricing(country_code: str, company_name: str) -> str:
    """Get pricing options for a specific country and company
    Args:
        country_code: two letter code of the user's country (e.g., US, UK)
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
    Returns:
        str: Pricing information for the specified country
    """
    try:
        pricing = prices.get_country_pricing(company_name, country_code)
        return json.dumps(pricing, indent=2)
    except KeyError as e:
        return f"Error: {str(e)}"

@mcp.tool()
def get_subscriptions(company_name: str, country_code: str) -> str:
    """Get all subscriptions (products) available for a company in a specific country
    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
        country_code: two letter code of the user's country (e.g., US, UK)
    Returns:
        str: List of all products
    """
    try:
        products = prices.get_subscriptions(company_name, country_code)
        return json.dumps(products, indent=2)
    except KeyError as e:
        return f"Error: {str(e)}"

@mcp.tool()
def get_resellers(country_code: str) -> str:
    """
    Get information on the resellers in a country.
    The absence of resellers does not mean that the product is unavailable in that country.
    
    Args:
        country_code: The county to query resellers in.
    Returns:
        str: A list of providers and their agents in the given country.
    """
    return json.dumps(get_providers(country_code), indent="\t")


if __name__ == "__main__":
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000)
    