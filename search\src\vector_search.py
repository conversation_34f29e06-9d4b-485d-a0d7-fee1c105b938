from typing import List, Tuple
from langchain_core.documents import Document
from langchain_community.vectorstores.timescalevector import TimescaleVector

import weaviate
from weaviate.classes.init import Auth

from weaviate.classes.query import Filter

from embedding import EmbeddingModelForSearch
from vector_store import WeaviateStore

import os
from dotenv import load_dotenv

load_dotenv()



class SearchService:
    def __init__(self):
        # Load environment variables
        self.service_url = ""
        self.collection_name = os.environ.get("VECTOR_STORE_COLLECTION", "rag_chunks")
        self.embedding_model = EmbeddingModelForSearch().embedding_model
        # Initialize TimescaleVector
        self.client = client = weaviate.connect_to_custom(
                http_host=os.getenv("WEAVIATE_HTTP_HOST"),
                http_port=os.getenv("WEAVIATE_HTTP_PORT"),
                http_secure=False,
                grpc_host=os.getenv("WEAVIATE_GRPC_HOST"),
                grpc_port=os.getenv("WEAVIATE_GRPC_PORT"),
                grpc_secure=False,
                auth_credentials=Auth.api_key(os.getenv("WEAVIATE_API_KEY"))
            ) 
    
    def vector_store(self, domain: str):
        return WeaviateStore(
            embedding_model=self.embedding_model,
            collection_name=domain,
            client=self.client
        )

    def search_query(self, domain: str, query: str, k: int = 20):
        """Run similarity search for multiple queries and return deduplicated top results."""

        result = self.vector_store(domain).vector_store.similarity_search(query=query, k=k)

        return result
    
    def search_chunks_by_source(self, domain: str, source: str):
        search_filter = Filter.by_property("source").equal(source)

        filter_result = self.vector_store(domain).vector_store.similarity_search(query="", filters=search_filter, k=4000)

        final_result = []
        for r in filter_result:
            if(r.metadata.get("source") == source):
                final_result.append(r)

        return final_result

    def client_close(self):
        self.client.close()

